#!/usr/bin/env python3
"""
Fraud Detection Platform - Ultra-Simple Startup Script
Start the entire platform with a single command.
"""

import os
import sys
import subprocess
import time
import signal
import platform
import argparse
import requests
from pathlib import Path
from threading import Thread

class FraudPlatformStarter:
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.processes = []
        self.running = False
        
        # Determine Python executable in venv
        if platform.system() == 'Windows':
            self.python_exe = self.project_root / 'venv' / 'Scripts' / 'python.exe'
        else:
            self.python_exe = self.project_root / 'venv' / 'bin' / 'python'
    
    def check_setup(self):
        """Check if setup has been run"""
        venv_path = self.project_root / 'venv'
        if not venv_path.exists():
            print("❌ Virtual environment not found. Please run 'python setup.py' first.")
            return False
        
        if not self.python_exe.exists():
            print("❌ Python executable not found in virtual environment.")
            return False
        
        dashboard_modules = self.project_root / 'dashboard' / 'node_modules'
        if not dashboard_modules.exists():
            print("❌ Node modules not found. Please run 'python setup.py' first.")
            return False
        
        return True
    
    def wait_for_service(self, url, service_name, timeout=30):
        """Wait for a service to become available"""
        print(f"⏳ Waiting for {service_name} to start...")
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                response = requests.get(url, timeout=2)
                if response.status_code == 200:
                    print(f"✅ {service_name} is ready!")
                    return True
            except requests.exceptions.RequestException:
                pass
            time.sleep(1)
        
        print(f"⚠️ {service_name} did not start within {timeout} seconds")
        return False
    
    def start_fraud_api(self):
        """Start the consolidated fraud API service"""
        print("🚀 Starting Fraud API service...")
        
        # Set environment variables
        env = os.environ.copy()
        env['PYTHONPATH'] = str(self.project_root)
        env['DATABASE_URL'] = f"sqlite:///{self.project_root}/fraud.db"
        env['RISK_THRESHOLD'] = '0.7'
        
        # Start the service
        process = subprocess.Popen([
            str(self.python_exe), '-m', 'uvicorn', 
            'fraud-api.main:app', 
            '--host', '0.0.0.0', 
            '--port', '8000',
            '--reload'
        ], cwd=self.project_root, env=env)
        
        self.processes.append(('Fraud API', process))
        
        # Wait for service to be ready
        return self.wait_for_service('http://localhost:8000/health', 'Fraud API')
    
    def start_dashboard(self):
        """Start the React dashboard"""
        print("🌐 Starting React dashboard...")
        
        dashboard_path = self.project_root / 'dashboard'
        
        # Set environment variables for React
        env = os.environ.copy()
        env['REACT_APP_API_URL'] = 'http://localhost:8000'
        env['REACT_APP_WS_URL'] = 'ws://localhost:8000/ws'
        env['BROWSER'] = 'none'  # Don't auto-open browser
        
        # Start React development server
        process = subprocess.Popen([
            'npm', 'start'
        ], cwd=dashboard_path, env=env)
        
        self.processes.append(('Dashboard', process))
        
        # Wait for dashboard to be ready
        return self.wait_for_service('http://localhost:3000', 'Dashboard', timeout=60)
    
    def start_platform(self):
        """Start the entire platform"""
        print("🚀 Starting Fraud Detection Platform")
        print("=" * 50)
        
        if not self.check_setup():
            return False
        
        try:
            # Start fraud API service
            if not self.start_fraud_api():
                print("❌ Failed to start Fraud API service")
                return False
            
            # Start dashboard
            if not self.start_dashboard():
                print("❌ Failed to start Dashboard")
                return False
            
            self.running = True
            
            # Print success message
            print("\n" + "=" * 60)
            print("🎉 FRAUD DETECTION PLATFORM STARTED SUCCESSFULLY!")
            print("=" * 60)
            print("📊 Dashboard:      http://localhost:3000")
            print("🔧 API Service:    http://localhost:8000")
            print("📖 API Docs:       http://localhost:8000/docs")
            print("🗄️ Database:       fraud.db")
            print("\n🔐 Login Credentials:")
            print("   Username: analyst")
            print("   Password: password")
            print("\n⚡ Quick Test:")
            print("   curl http://localhost:8000/health")
            print("\n⌨️  Press Ctrl+C to stop all services")
            print("=" * 60)
            
            return True
            
        except Exception as e:
            print(f"❌ Error starting platform: {e}")
            return False
    
    def stop_platform(self):
        """Stop all running services"""
        print("\n🛑 Stopping Fraud Detection Platform...")
        
        for service_name, process in self.processes:
            try:
                print(f"🛑 Stopping {service_name}...")
                process.terminate()
                
                # Wait for graceful shutdown
                try:
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    print(f"⚠️ Force killing {service_name}...")
                    process.kill()
                    process.wait()
                    
            except Exception as e:
                print(f"⚠️ Error stopping {service_name}: {e}")
        
        self.processes.clear()
        self.running = False
        print("✅ All services stopped")
    
    def signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        self.stop_platform()
        sys.exit(0)
    
    def run(self, stop_only=False):
        """Main run method"""
        if stop_only:
            # Just stop any running services (best effort)
            print("🛑 Attempting to stop any running services...")
            
            # Try to stop common processes
            try:
                # Kill any uvicorn processes on port 8000
                if platform.system() == 'Windows':
                    subprocess.run(['taskkill', '/F', '/IM', 'python.exe'], 
                                 capture_output=True)
                else:
                    subprocess.run(['pkill', '-f', 'uvicorn.*8000'], 
                                 capture_output=True)
                
                # Kill any npm processes
                if platform.system() == 'Windows':
                    subprocess.run(['taskkill', '/F', '/IM', 'node.exe'], 
                                 capture_output=True)
                else:
                    subprocess.run(['pkill', '-f', 'npm.*start'], 
                                 capture_output=True)
                    
            except Exception:
                pass
            
            print("✅ Stop command completed")
            return
        
        # Set up signal handlers
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        # Start the platform
        if self.start_platform():
            try:
                # Keep the script running
                while self.running:
                    time.sleep(1)
            except KeyboardInterrupt:
                self.signal_handler(signal.SIGINT, None)
        else:
            print("❌ Failed to start platform")
            self.stop_platform()
            sys.exit(1)

def main():
    parser = argparse.ArgumentParser(description='Fraud Detection Platform Starter')
    parser.add_argument('--stop', action='store_true', 
                       help='Stop all running services')
    parser.add_argument('--version', action='version', version='2.0.0')
    
    args = parser.parse_args()
    
    starter = FraudPlatformStarter()
    starter.run(stop_only=args.stop)

if __name__ == "__main__":
    main()
